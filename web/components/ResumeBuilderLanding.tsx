"use client";

import React from "react";

// Icon components
const BotIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
);

const PencilIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z" />
  </svg>
);

const TokenIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
    <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
    <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
    <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
  </svg>
);

interface ResumeBuilderLandingProps {
  onSelectPath: (path: 'ai-powered' | 'manual') => void;
}

const ResumeBuilderLanding: React.FC<ResumeBuilderLandingProps> = ({ onSelectPath }) => {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Bagaimana Anda ingin membuat CV?</h2>
        <p className="text-gray-600 text-lg">Pilih metode yang paling sesuai dengan kebutuhan Anda</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {/* AI-Powered Resume Path */}
        <button
          onClick={() => onSelectPath('ai-powered')}
          className="group p-8 border-2 border-gray-200 rounded-2xl hover:border-primary hover:shadow-xl transition-all duration-300 text-left h-full"
        >
          <div className="flex flex-col items-center space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <BotIcon className="w-10 h-10 text-white" />
            </div>

            <div className="space-y-3 text-center">
              <h3 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
                CV Spesifik Pekerjaan
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700">
                Disesuaikan untuk pekerjaan tertentu dengan bantuan AI
              </p>

              {/* Pricing Badge */}
              <div className="inline-flex items-center bg-amber-50 border border-amber-200 text-amber-700 px-3 py-1 rounded-full">
                <TokenIcon className="w-4 h-4 text-amber-600 mr-1" />
                <span className="text-sm font-semibold">25 Token</span>
              </div>
            </div>

            <div className="space-y-3 text-sm text-gray-500 group-hover:text-gray-600 w-full">
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Kata kunci yang dioptimalkan</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Disesuaikan dengan deskripsi pekerjaan</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Peluang wawancara lebih tinggi</span>
              </div>
            </div>

            <div className="w-full pt-4 border-t border-gray-100 text-center">
              <span className="text-primary font-semibold group-hover:underline">
                Mulai dengan AI →
              </span>
            </div>
          </div>
        </button>

        {/* Manual Resume Path */}
        <button
          onClick={() => onSelectPath('manual')}
          className="group p-8 border-2 border-gray-200 rounded-2xl hover:border-primary hover:shadow-xl transition-all duration-300 text-left h-full"
        >
          <div className="flex flex-col items-center space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <PencilIcon className="w-10 h-10 text-white" />
            </div>

            <div className="space-y-3 text-center">
              <h3 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
                Buat Manual
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700">
                Kontrol penuh dengan editor CV yang canggih
              </p>

              {/* Pricing Badge */}
              <div className="inline-flex items-center bg-amber-50 border border-amber-200 text-amber-700 px-3 py-1 rounded-full">
                <TokenIcon className="w-4 h-4 text-amber-600 mr-1" />
                <span className="text-sm font-semibold">15 Token</span>
                <span className="text-xs text-amber-600 ml-1">(saat download)</span>
              </div>
            </div>

            <div className="space-y-3 text-sm text-gray-500 group-hover:text-gray-600 w-full">
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Fleksibel untuk berbagai posisi</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Kontrol penuh atas konten</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-left">Siap pakai kapan saja</span>
              </div>
            </div>

            <div className="w-full pt-4 border-t border-gray-100 text-center">
              <span className="text-primary font-semibold group-hover:underline">
                Mulai Manual →
              </span>
            </div>
          </div>
        </button>
      </div>

      <div className="text-center text-sm text-gray-500 max-w-2xl mx-auto">
        <p>
          <strong>CV Spesifik Pekerjaan:</strong> Unggah CV lama + masukkan deskripsi pekerjaan → AI membuat CV yang disesuaikan<br />
          <strong>Buat Manual:</strong> Langsung ke editor → Isi semua detail sendiri dengan bantuan template
        </p>
      </div>
    </div>
  );
};

export default ResumeBuilderLanding;